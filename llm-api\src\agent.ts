import { ToolFunction, ToolSpec } from './types';
import { getToolSpec } from './tools';

/**
 * Agent definition with instructions and tools.
 * 
 * An Agent represents an LLM-powered assistant with specific instructions
 * and a set of tools it can use to accomplish tasks.
 */
export class Agent {
  public name: string;
  public instructions: string;
  public tools: ToolFunction[];
  public handoffDescription?: string;

  constructor(
    name: string,
    instructions: string,
    tools: ToolFunction[] = [],
    handoffDescription?: string
  ) {
    this.name = name;
    this.instructions = instructions;
    this.tools = tools;
    this.handoffDescription = handoffDescription;
  }
  
  /**
   * Get OpenAI-compatible tool specifications.
   * 
   * @returns List of tool specifications in the format expected by OpenAI API
   */
  getToolSpecs(): ToolSpec[] {
    const specs: ToolSpec[] = [];
    
    for (const tool of this.tools) {
      const spec = getToolSpec(tool);
      if (spec) {
        specs.push(spec);
      }
    }
    
    return specs;
  }
  
  /**
   * Get mapping from tool names to their implementations.
   * 
   * @returns Map of tool names to their function implementations
   */
  getToolMap(): Map<string, ToolFunction> {
    const mapping = new Map<string, ToolFunction>();
    
    for (const tool of this.tools) {
      const spec = getToolSpec(tool);
      const exposedName = spec?.function?.name || tool.name;
      mapping.set(exposedName, tool);
    }
    
    return mapping;
  }
  
  /**
   * Add a tool to the agent
   */
  addTool(tool: ToolFunction): void {
    this.tools.push(tool);
  }
  
  /**
   * Remove a tool from the agent
   */
  removeTool(toolName: string): boolean {
    const index = this.tools.findIndex(tool => tool.name === toolName);
    if (index !== -1) {
      this.tools.splice(index, 1);
      return true;
    }
    return false;
  }
  
  /**
   * Check if the agent has a specific tool
   */
  hasTool(toolName: string): boolean {
    return this.tools.some(tool => tool.name === toolName);
  }
}
