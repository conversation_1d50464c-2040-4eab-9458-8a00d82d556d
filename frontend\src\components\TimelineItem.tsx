import React from 'react';
import MessageFormatter from './MessageFormatter';

interface TimelineItemProps {
  item: {
    type: 'message' | 'tool_call';
    id: bigint;
    createdAt: Date;
    sequence: number;
  } & (
    | {
        type: 'message';
        content: string;
        agentId: string | null;
        userId: bigint | null;
        customerId: bigint | null;
        imageUrl: string | null;
        videoUrl: string | null;
        attachmentUrl: string | null;
        attachmentType: string | null;
        cost: number | null;
        executionTime: number | null;
        inputTokens: number | null;
        outputTokens: number | null;
        user: {
          id: bigint;
          email: string;
          name: string | null;
        } | null;
        customer: {
          id: bigint;
          email: string | null;
          name: string | null;
        } | null;
      }
    | {
        type: 'tool_call';
        toolName: string;
        toolInput?: any;
        toolOutput?: any;
        parameters?: any; // Backend compatibility
        result?: any; // Backend compatibility
        status?: string;
        error?: string;
        duration?: number;
        executionTime: number | null;
        success: boolean;
        errorMessage: string | null;
        cost: number | null;
        inputTokens: number | null;
        outputTokens: number | null;
        createdByUser?: {
          id: bigint;
          email: string;
          name: string | null;
        } | null;
      }
  );
  showMetrics?: boolean; // Controls whether to show performance metrics
  showToolCallDetails?: boolean; // Controls whether to show tool call details
}

// Icon Components
const UserIcon = ({ name }: { name: string }) => (
  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-blue-400/30">
    {name.charAt(0).toUpperCase()}
  </div>
);

const AIAgentIcon = () => (
  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-emerald-500 to-cyan-500 flex items-center justify-center text-white shadow-lg border border-emerald-400/30">
    <svg className="w-4 h-4 md:w-5 md:w-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
    </svg>
  </div>
);

const CustomerIcon = ({ name }: { name: string }) => (
  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30">
    {name.charAt(0).toUpperCase()}
  </div>
);

const ToolIcon = () => (
  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-amber-500 to-yellow-500 flex items-center justify-center text-white shadow-lg border border-amber-400/30">
    <svg className="w-4 h-4 md:w-5 md:w-5" fill="currentColor" viewBox="0 0 24 24">
      <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
    </svg>
  </div>
);

// Performance Metrics Component
const PerformanceMetrics = ({
  cost,
  executionTime,
  inputTokens,
  outputTokens,
  variant = 'default'
}: {
  cost: number | null | undefined;
  executionTime: number | null | undefined;
  inputTokens: number | null | undefined;
  outputTokens: number | null | undefined;
  variant?: 'default' | 'tool';
}) => {
  // Convert null/undefined to 0 and ensure numeric values
  const safeCost = Number(cost) || 0;
  const safeExecutionTime = Number(executionTime) || 0;
  const safeInputTokens = Number(inputTokens) || 0;
  const safeOutputTokens = Number(outputTokens) || 0;

  const hasMetrics = safeCost > 0 || safeExecutionTime > 0 || safeInputTokens > 0 || safeOutputTokens > 0;
  
  if (!hasMetrics) return null;

  const baseClasses = variant === 'tool' 
    ? "bg-amber-500/10 border border-amber-400/20 rounded-md p-2 mb-2"
    : "mt-3 pt-3 border-t border-current/20";

  return (
    <div className={baseClasses}>
      <div className="text-xs font-medium opacity-80 mb-2">Performance Metrics:</div>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="opacity-80">
          💰 Cost: ${safeCost.toFixed(6)}
        </div>
        <div className="opacity-80">
          ⏱️ Time: {safeExecutionTime}ms
        </div>
        <div className="opacity-80">
          📥 Input: {safeInputTokens} tokens
        </div>
        <div className="opacity-80">
          📤 Output: {safeOutputTokens} tokens
        </div>
      </div>
    </div>
  );
};

export default function TimelineItem({ item, showMetrics = true, showToolCallDetails = true }: TimelineItemProps) {
  const timestamp = new Date(item.createdAt);

  if (item.type === 'message') {
    // Check if this is a customer message by looking at the content format
    const isCustomerMessage = item.content && item.content.startsWith('[') && item.content.includes(']:');
    const isAgent = !item.user && !item.customer && !isCustomerMessage;
    const isCustomer = !!item.customer || isCustomerMessage;
    const isUser = !!item.user;
    
    // Extract customer name from message content if it's a customer message
    let senderName = 'Unknown';
    if (isCustomerMessage) {
      const match = item.content.match(/^\[([^\]]+)\]:/);
      senderName = match ? match[1] : 'Customer';
    } else if (item.customer?.name || item.customer?.email) {
      senderName = (item.customer.name || item.customer.email) || 'Customer';
    } else if (isAgent) {
      senderName = 'AI Agent';
    } else if (item.user?.name || item.user?.email) {
      senderName = (item.user.name || item.user.email) || 'User';
    }

    return (
      <div className={`flex gap-3 md:gap-4 ${isCustomer ? 'justify-end' : 'justify-start'}`}>
        {/* Left side icons for agent messages */}
        {!isCustomer && (
          <div className="flex-shrink-0 mt-1">
            {isAgent ? (
              <AIAgentIcon />
            ) : (
              <UserIcon name={senderName} />
            )}
          </div>
        )}
        
        <div className={`max-w-[85%] md:max-w-[70%] ${isCustomer ? 'order-1' : ''}`}>
          <div className={`px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg ${
            isAgent 
              ? 'bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100' 
              : isCustomer
              ? 'bg-gradient-to-br from-orange-500/20 to-red-500/20 border border-orange-400/30 text-orange-100'
              : 'bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-400/30 text-blue-100'
          }`}>
            <div className="flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2">
              <span className="font-medium text-xs md:text-sm opacity-90" style={{ fontFamily: 'Exo 2, sans-serif' }}>
                {senderName}
              </span>
              <span className="text-xs opacity-60" style={{ fontFamily: 'Exo 2, sans-serif' }}>
                {isNaN(timestamp.getTime()) ? '' : timestamp.toLocaleTimeString()}
              </span>
            </div>
            <div style={{ fontFamily: 'Exo 2, sans-serif' }}>
              <MessageFormatter content={isCustomerMessage ? item.content.replace(/^\[[^\]]+\]:\s*/, '') : item.content} />
            </div>

            {/* Performance Metrics */}
            {showMetrics && (
              <PerformanceMetrics
                cost={item.cost}
                executionTime={item.executionTime}
                inputTokens={item.inputTokens}
                outputTokens={item.outputTokens}
              />
            )}

            {/* Attachments */}
            {(item.imageUrl || item.videoUrl || item.attachmentUrl) && (
              <div className="mt-3 pt-3 border-t border-current/20">
                <div className="text-xs font-medium opacity-80 mb-2">Attachments:</div>
                <div className="space-y-1">
                  {item.imageUrl && (
                    <div className="text-xs opacity-80">
                      📷 Image: <a href={item.imageUrl} target="_blank" rel="noopener noreferrer" className="underline hover:opacity-100">{item.imageUrl}</a>
                    </div>
                  )}
                  {item.videoUrl && (
                    <div className="text-xs opacity-80">
                      🎥 Video: <a href={item.videoUrl} target="_blank" rel="noopener noreferrer" className="underline hover:opacity-100">{item.videoUrl}</a>
                    </div>
                  )}
                  {item.attachmentUrl && (
                    <div className="text-xs opacity-80">
                      📎 {item.attachmentType || 'File'}: <a href={item.attachmentUrl} target="_blank" rel="noopener noreferrer" className="underline hover:opacity-100">{item.attachmentUrl}</a>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right side icons for customer messages */}
        {isCustomer && (
          <div className="flex-shrink-0 mt-1 order-2">
            <CustomerIcon name={senderName} />
          </div>
        )}
      </div>
    );
  }

  // Tool call item - hide completely if showToolCallDetails is false
  if (!showToolCallDetails) {
    return null;
  }

  return (
    <div className="flex gap-3 md:gap-4 justify-start">
      <div className="flex-shrink-0 mt-1">
        <ToolIcon />
      </div>

      <div className="max-w-[85%] md:max-w-[70%]">
        <div className="px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-amber-500/20 to-yellow-500/20 border border-amber-400/30 text-amber-100">
          <div className="flex items-center justify-between gap-2 md:gap-4 mb-1 md:mb-2">
            <span className="font-medium text-xs md:text-sm opacity-90" style={{ fontFamily: 'Exo 2, sans-serif' }}>
              🔧 {item.toolName}
            </span>
            <span className="text-xs opacity-60" style={{ fontFamily: 'Exo 2, sans-serif' }}>
              {isNaN(timestamp.getTime()) ? '' : timestamp.toLocaleTimeString()}
            </span>
          </div>

          <div className="space-y-2 text-xs">
            <div className="flex items-center gap-2">
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                item.success
                  ? 'bg-emerald-500/10 text-emerald-300 ring-1 ring-emerald-500/20'
                  : 'bg-red-500/10 text-red-300 ring-1 ring-red-500/20'
              }`}>
                {item.success ? '✅ Success' : '❌ Failed'}
              </span>
              {showMetrics && item.executionTime && (
                <span className="text-amber-300/80">
                  ⏱️ {item.executionTime}ms
                </span>
              )}
            </div>

            {/* Performance Metrics */}
            {showMetrics && (
              <PerformanceMetrics
                cost={item.cost}
                executionTime={item.executionTime}
                inputTokens={item.inputTokens}
                outputTokens={item.outputTokens}
                variant="tool"
              />
            )}

            {item.toolInput && (
              <div>
                <div className="font-medium opacity-80 mb-1">Input:</div>
                <pre className="bg-amber-500/10 border border-amber-400/20 rounded-md p-2 text-xs overflow-x-auto">
                  {JSON.stringify(item.toolInput, null, 2)}
                </pre>
              </div>
            )}

            {item.success && item.toolOutput && (
              <div>
                <div className="font-medium opacity-80 mb-1">Output:</div>
                <pre className="bg-emerald-500/10 border border-emerald-500/20 rounded-md p-2 text-xs overflow-x-auto">
                  {JSON.stringify(item.toolOutput, null, 2)}
                </pre>
              </div>
            )}

            {!item.success && item.errorMessage && (
              <div>
                <div className="font-medium opacity-80 mb-1 text-red-300">Error:</div>
                <div className="bg-red-500/10 border border-red-500/20 rounded-md p-2 text-xs text-red-200">
                  {item.errorMessage}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

