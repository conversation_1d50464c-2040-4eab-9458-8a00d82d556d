import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Product } from './product.entity';

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productsRepository: Repository<Product>,
  ) {}

  async findAll(): Promise<Product[]> {
    return this.productsRepository.find({
      where: { isDeleted: false },
      relations: ['store'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productsRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    return product;
  }

  async findByStoreId(storeId: string): Promise<Product[]> {
    return this.productsRepository.find({
      where: { storeId, isDeleted: false },
      relations: ['store'],
      order: { createdAt: 'DESC' },
    });
  }

  async create(createProductDto: Partial<Product> & { userId?: string | number }): Promise<Product> {
    // Extract userId and map it to the correct fields
    const { userId, ...productData } = createProductDto;

    // Create the product with proper field mapping
    const product = this.productsRepository.create({
      ...productData,
      createdBy: userId?.toString(),
    });

    return this.productsRepository.save(product);
  }

  async update(id: string, updateProductDto: Partial<Product>): Promise<Product> {
    const product = await this.findOne(id);
    Object.assign(product, updateProductDto);
    return this.productsRepository.save(product);
  }

  async remove(id: string): Promise<void> {
    const product = await this.findOne(id);
    product.isDeleted = true;
    await this.productsRepository.save(product);
  }
}
