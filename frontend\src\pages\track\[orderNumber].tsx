import { useEffect, useMemo, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { orderApi } from '../../utils/api';
import { usePreferences } from '../../context/PreferencesContext';

interface OrderStatus {
  value: string;
  label: string;
  color: string;
  description: string;
}

type LangKey = 'en' | 'fr' | 'ar';
const baseStatuses = [
  { value: 'draft', key: 'draft' },
  { value: 'confirmed', key: 'confirmed' },
  { value: 'processing', key: 'processing' },
  { value: 'shipped', key: 'shipped' },
  { value: 'delivered', key: 'delivered' },
  { value: 'cancelled', key: 'cancelled' },
  { value: 'returned', key: 'returned' },
] as const;

const messages: Record<LangKey, Record<string, string>> = {
  en: {
    title: 'Track Order - Teno Store',
    metaDesc: 'Track your order status',
    loading: 'Loading order information...',
    enterOrderTitle: 'Enter Order Number',
    enterOrderText: 'Please go to the order tracking page and enter your order number.',
    goToTracking: 'Go to Order Tracking',
    notFoundTitle: 'Order Not Found',
    notFoundText: 'The order number you entered could not be found.',
    goHome: 'Go Home',
    header: 'Order Tracking',
    subHeader: 'Track your order status and delivery information',
    order: 'Order',
    placedOn: 'Placed on',
    expectedDelivery: 'Expected Delivery',
    orderProgress: 'Order Progress',
    orderItems: 'Order Items',
    quantity: 'Quantity',
    unitPrice: 'Unit Price',
    orderSummary: 'Order Summary',
    subtotal: 'Subtotal:',
    tax: 'Tax:',
    total: 'Total:',
    backToStore: 'Back to Store',
    draft: 'Order Received',
    confirmed: 'Order Confirmed',
    processing: 'Preparing Order',
    shipped: 'Shipped',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
    returned: 'Returned',
    draftDesc: 'Your order has been received and is being processed.',
    confirmedDesc: 'Your order has been confirmed and is being prepared.',
    processingDesc: 'Your order is being prepared for shipment.',
    shippedDesc: 'Your order has been shipped and is on its way.',
    deliveredDesc: 'Your order has been successfully delivered.',
    cancelledDesc: 'Your order has been cancelled.',
    returnedDesc: 'Your order has been returned.',
    cancellationReasonLabel: 'Cancellation reason',
  },
  fr: {
    title: 'Suivre la commande - Teno Store',
    metaDesc: 'Suivez le statut de votre commande',
    loading: 'Chargement des informations de commande...',
    enterOrderTitle: 'Saisir le numéro de commande',
    enterOrderText: 'Veuillez aller à la page de suivi et saisir votre numéro de commande.',
    goToTracking: 'Aller au suivi de commande',
    notFoundTitle: 'Commande introuvable',
    notFoundText: 'Le numéro de commande saisi est introuvable.',
    goHome: 'Accueil',
    header: 'Suivi de commande',
    subHeader: 'Suivez le statut et les informations de livraison',
    order: 'Commande',
    placedOn: 'Passée le',
    expectedDelivery: 'Livraison prévue',
    orderProgress: 'Progression de la commande',
    orderItems: 'Articles de la commande',
    quantity: 'Quantité',
    unitPrice: 'Prix unitaire',
    orderSummary: 'Récapitulatif de commande',
    subtotal: 'Sous-total :',
    tax: 'Taxe :',
    total: 'Total :',
    backToStore: 'Retour à la boutique',
    draft: 'Commande reçue',
    confirmed: 'Commande confirmée',
    processing: 'Préparation de la commande',
    shipped: 'Expédiée',
    delivered: 'Livrée',
    cancelled: 'Annulée',
    returned: 'Retournée',
    draftDesc: 'Votre commande a été reçue et est en cours de traitement.',
    confirmedDesc: 'Votre commande a été confirmée et est en préparation.',
    processingDesc: 'Votre commande est en préparation pour l’expédition.',
    shippedDesc: 'Votre commande a été expédiée et est en route.',
    deliveredDesc: 'Votre commande a été livrée avec succès.',
    cancelledDesc: 'Votre commande a été annulée.',
    returnedDesc: 'Votre commande a été retournée.',
    cancellationReasonLabel: "Raison de l'annulation",
  },
  ar: {
    title: 'تتبع الطلب - متجر تينو',
    metaDesc: 'تتبع حالة طلبك',
    loading: 'جارٍ تحميل معلومات الطلب...',
    enterOrderTitle: 'أدخل رقم الطلب',
    enterOrderText: 'يرجى الذهاب إلى صفحة تتبع الطلب وإدخال رقم الطلب.',
    goToTracking: 'الذهاب إلى تتبع الطلب',
    notFoundTitle: 'الطلب غير موجود',
    notFoundText: 'رقم الطلب الذي أدخلته غير موجود.',
    goHome: 'الصفحة الرئيسية',
    header: 'تتبع الطلب',
    subHeader: 'تتبع حالة الطلب ومعلومات التسليم',
    order: 'الطلب',
    placedOn: 'تم الطلب في',
    expectedDelivery: 'موعد التسليم المتوقع',
    orderProgress: 'تقدم الطلب',
    orderItems: 'عناصر الطلب',
    quantity: 'الكمية',
    unitPrice: 'سعر الوحدة',
    orderSummary: 'ملخص الطلب',
    subtotal: 'المجموع الفرعي:',
    tax: 'الضريبة:',
    total: 'الإجمالي:',
    backToStore: 'العودة إلى المتجر',
    draft: 'تم استلام الطلب',
    confirmed: 'تم تأكيد الطلب',
    processing: 'جاري تجهيز الطلب',
    shipped: 'تم الشحن',
    delivered: 'تم التسليم',
    cancelled: 'تم الإلغاء',
    returned: 'تم الإرجاع',
    draftDesc: 'تم استلام طلبك وهو قيد المعالجة.',
    confirmedDesc: 'تم تأكيد طلبك وهو قيد التحضير.',
    processingDesc: 'يتم تجهيز طلبك للشحن.',
    shippedDesc: 'تم شحن طلبك وهو في الطريق.',
    deliveredDesc: 'تم تسليم طلبك بنجاح.',
    cancelledDesc: 'تم إلغاء طلبك.',
    returnedDesc: 'تم إرجاع طلبك.',
    cancellationReasonLabel: 'سبب الإلغاء',
  },
};

const ORDER_STATUSES_FOR = (t: Record<string, string>): OrderStatus[] => ([
  { value: 'draft', label: t.draft, color: 'text-slate-300', description: t.draftDesc },
  { value: 'confirmed', label: t.confirmed, color: 'text-emerald-300', description: t.confirmedDesc },
  { value: 'processing', label: t.processing, color: 'text-cyan-300', description: t.processingDesc },
  { value: 'shipped', label: t.shipped, color: 'text-indigo-300', description: t.shippedDesc },
  { value: 'delivered', label: t.delivered, color: 'text-emerald-300', description: t.deliveredDesc },
  { value: 'cancelled', label: t.cancelled, color: 'text-red-300', description: t.cancelledDesc },
  { value: 'returned', label: t.returned, color: 'text-orange-300', description: t.returnedDesc },
]);

const getStatusStep = (status: string, statuses: OrderStatus[]): number => {
  const statusIndex = statuses.findIndex((s: OrderStatus) => s.value === status);
  return statusIndex >= 0 ? statusIndex : 0;
};

const isCompleted = (currentStep: number, step: number): boolean => {
  return step <= currentStep;
};

const isCurrent = (currentStep: number, step: number): boolean => {
  return step === currentStep;
};

export default function TrackOrderPage() {
  const router = useRouter();
  const { orderNumber } = router.query;
  const { language, currency, setLanguage } = usePreferences();
  const [order, setOrder] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Normalize order number and check if ready
  const normalizedOrderNumber = useMemo(() => {
    if (typeof orderNumber === 'string') {
      return orderNumber.trim().toUpperCase();
    }
    return null;
  }, [orderNumber]);

  const isReady = router.isReady;

  // Extract order currency early for currency formatter
  const orderCurrency = order?.currency || 'USD';

  const langKey = useMemo<LangKey>(() => {
    const lng = (language || 'en').toLowerCase();
    if (lng.startsWith('fr')) return 'fr';
    if (lng.startsWith('ar')) return 'ar';
    return 'en';
  }, [language]);
  const t = messages[langKey];
  const dir = langKey === 'ar' ? 'rtl' : 'ltr';
  const ORDER_STATUSES = useMemo(() => ORDER_STATUSES_FOR(t), [t]);
  const currencyFormatter = useMemo(() => {
    try {
      return new Intl.NumberFormat(language || 'en', { style: 'currency', currency: orderCurrency });
    } catch {
      return new Intl.NumberFormat('en', { style: 'currency', currency: 'USD' });
    }
  }, [language, orderCurrency]);

  // Sync language from query parameter (?lang=en|fr|ar) and persist via preferences
  useEffect(() => {
    if (!router.isReady) return;
    const qlang = router.query.lang;
    if (typeof qlang !== 'string') return;
    const normalized = qlang.toLowerCase();
    const map: Record<string, string> = {
      en: 'en-US',
      fr: 'fr-FR',
      ar: 'ar',
    };
    const next = map[normalized] || qlang;
    if (next && next !== language) {
      setLanguage(next as any);
    }
  }, [router.isReady, router.query.lang, setLanguage, language]);

  // Get order by order number
  const fetchOrder = async () => {
    if (!normalizedOrderNumber) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const result = await orderApi.getByOrderNumber(normalizedOrderNumber);
      if (result && typeof result === 'object' && 'data' in result) {
        setOrder(result.data || result);
      } else {
        setOrder(result);
      }
    } catch (err) {
      setError('Failed to load order information');
      console.error('Error fetching order:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch order when dependencies change
  useEffect(() => {
    if (isReady && normalizedOrderNumber) {
      fetchOrder();
    }
  }, [isReady, normalizedOrderNumber]);

  // If the router isn't ready yet, keep showing loading state
  if (!isReady) {
    return (
      <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
        <Head>
          <title>{t.title}</title>
          <meta name="description" content={t.metaDesc} />
        </Head>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t.loading}</p>
        </div>
      </div>
    );
  }

  // Graceful state when route is ready but no order number provided
  if (isReady && !normalizedOrderNumber) {
    return (
      <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
        <Head>
          <title>{t.title}</title>
          <meta name="description" content={t.metaDesc} />
        </Head>
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100 mb-6">
              <svg className="h-7 w-7 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.enterOrderTitle}</h2>
            <p className="text-gray-600 mb-6">{t.enterOrderText}</p>
            <button
              onClick={() => router.push('/track')}
              className="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors"
            >
              {t.goToTracking}
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
        <Head>
          <title>{t.title}</title>
          <meta name="description" content={t.metaDesc} />
        </Head>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t.loading}</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
        <Head>
          <title>{t.notFoundTitle} - Teno Store</title>
          <meta name="description" content={t.metaDesc} />
        </Head>
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 mb-6">
              <svg className="h-7 w-7 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">{t.notFoundTitle}</h2>
            <p className="text-gray-600 mb-6">{error || t.notFoundText}</p>
            <button
              onClick={() => router.push('/')}
              className="bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors"
            >
              {t.goHome}
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentStatusStep = getStatusStep(order.status, ORDER_STATUSES);
  const currentStatus = ORDER_STATUSES[currentStatusStep];
  const orderDate = new Date(order.orderDate);
  const expectedDeliveryDate = order.expectedDeliveryDate ? new Date(order.expectedDeliveryDate) : null;
  const subtotal = parseFloat(order.subtotal?.toString?.() ?? '0') || 0;
  const taxAmount = parseFloat(order.taxAmount?.toString?.() ?? '0') || 0;
  const total = parseFloat(order.total?.toString?.() ?? '0') || 0;

  // Filter statuses for display (exclude cancelled/returned unless current status)
  const displayStatuses = ORDER_STATUSES.filter(status => {
    if (order.status === 'cancelled' || order.status === 'returned') {
      return status.value === order.status || ['draft', 'confirmed'].includes(status.value);
    }
    return !['cancelled', 'returned'].includes(status.value);
  });

  return (
    <div dir={dir} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Head>
        <title>{t.title}</title>
        <meta name="description" content={t.metaDesc} />
      </Head>

      <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-indigo-100 mb-6">
            <svg className="h-10 w-10 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <h1 className="text-4xl font-extrabold text-gray-900 mb-2">{t.header}</h1>
          <p className="text-lg text-gray-600">{t.subHeader}</p>
        </div>

        {/* Language Switcher */}
        <div className="flex justify-end mb-6">
          <select
            aria-label="Language"
            value={langKey}
            onChange={(e) => {
              const val = e.target.value as 'en' | 'fr' | 'ar';
              const map: Record<string, string> = { en: 'en-US', fr: 'fr-FR', ar: 'ar' };
              setLanguage(map[val] as any);
            }}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="en">English</option>
            <option value="fr">Français</option>
            <option value="ar">العربية</option>
          </select>
        </div>

        {/* Order Information Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">{t.order} {order.orderNumber}</h2>
              <p className="text-gray-600">{t.placedOn} {orderDate.toLocaleDateString()}</p>
            </div>
            <div className="mt-4 md:mt-0">
              <span className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white border ${
                currentStatus.color === 'text-emerald-300' ? 'border-emerald-200 text-emerald-800 bg-emerald-50' :
                currentStatus.color === 'text-cyan-300' ? 'border-cyan-200 text-cyan-800 bg-cyan-50' :
                currentStatus.color === 'text-indigo-300' ? 'border-indigo-200 text-indigo-800 bg-indigo-50' :
                currentStatus.color === 'text-yellow-300' ? 'border-yellow-200 text-yellow-800 bg-yellow-50' :
                currentStatus.color === 'text-red-300' ? 'border-red-200 text-red-800 bg-red-50' :
                currentStatus.color === 'text-orange-300' ? 'border-orange-200 text-orange-800 bg-orange-50' :
                'border-gray-200 text-gray-800 bg-gray-50'
              }`}>
                {currentStatus.label}
              </span>
            </div>
          </div>

          {/* Customer Orders Button - REMOVED */}

          {expectedDeliveryDate && order.status !== 'cancelled' && order.status !== 'returned' && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <div className="flex">
                <svg className="h-5 w-5 text-blue-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-blue-800">{t.expectedDelivery}</h3>
                  <p className="text-sm text-blue-700">{expectedDeliveryDate.toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}

          {order.status === 'cancelled' && order.cancellationReason && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.366-.446 1.12-.446 1.486 0l6.518 7.933c.46.56.052 1.418-.743 1.418H2.482c-.795 0-1.203-.858-.743-1.418l6.518-7.933zM11 13a1 1 0 10-2 0 1 1 0 002 0zm-1-2a1 1 0 01-1-1V7a1 1 0 112 0v3a1 1 0 01-1 1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-red-800">{t.cancellationReasonLabel}</h3>
                  <p className="text-sm text-red-700">{order.cancellationReason}</p>
                </div>
              </div>
            </div>
          )}

          {/* Order Progress */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-6">{t.orderProgress}</h3>
            <div className="relative">
              {displayStatuses.map((status, index) => {
                const stepCompleted = isCompleted(currentStatusStep, ORDER_STATUSES.findIndex((s: OrderStatus) => s.value === status.value));
                const stepCurrent = isCurrent(currentStatusStep, ORDER_STATUSES.findIndex((s: OrderStatus) => s.value === status.value));
                
                return (
                  <div key={status.value} className="relative flex items-start mb-8 last:mb-0">
                    {/* Connector Line */}
                    {index < displayStatuses.length - 1 && (
                      <div className={`absolute left-6 top-12 w-0.5 h-8 ${
                        stepCompleted ? 'bg-emerald-500' : 'bg-gray-200'
                      }`} />
                    )}
                    
                    {/* Status Icon */}
                    <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                      stepCompleted 
                        ? 'bg-emerald-500 border-emerald-500' 
                        : stepCurrent
                        ? 'bg-indigo-500 border-indigo-500'
                        : 'bg-white border-gray-300'
                    }`}>
                      {stepCompleted ? (
                        <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : stepCurrent ? (
                        <div className="w-3 h-3 bg-white rounded-full" />
                      ) : (
                        <div className="w-3 h-3 bg-gray-300 rounded-full" />
                      )}
                    </div>
                    
                    {/* Status Content */}
                    <div className="ml-4 min-w-0 flex-1">
                      <h4 className={`text-sm font-medium ${
                        stepCompleted || stepCurrent ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {status.label}
                      </h4>
                      <p className={`text-sm ${
                        stepCompleted || stepCurrent ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {status.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t.orderItems}</h3>
          <div className="space-y-4">
            {order.items?.map((item: any) => {
              const unitPrice = parseFloat(item.unitPrice?.toString?.() ?? '0') || 0;
              const lineTotal = parseFloat(item.lineTotal?.toString?.() ?? '0') || 0;
              
              return (
                <div key={item.id.toString()} className="flex items-center justify-between py-4 border-b border-gray-200 last:border-b-0">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{item.productName}</h4>
                    <p className="text-sm text-gray-600">{t.quantity}: {item.quantity}</p>
                    <p className="text-sm text-gray-600">{t.unitPrice}: {currencyFormatter.format(unitPrice)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{currencyFormatter.format(lineTotal)}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">{t.orderSummary}</h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">{t.subtotal}</span>
              <span className="text-gray-900">{currencyFormatter.format(subtotal)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">{t.tax}</span>
              <span className="text-gray-900">{currencyFormatter.format(taxAmount)}</span>
            </div>
            <div className="border-t border-gray-200 pt-2">
              <div className="flex justify-between text-base font-medium">
                <span className="text-gray-900">{t.total}</span>
                <span className="text-gray-900">{currencyFormatter.format(total)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home removed as requested */}
      </div>
    </div>
  );
}
