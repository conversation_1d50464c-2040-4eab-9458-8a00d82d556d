import { createTool } from '../../../../../llm-api/dist';
import { filterCustomers } from '../../services/customer.service';
import { UNDERWAY_STATUSES, resolveItemsAgainstCatalog } from './shared';
import { In } from 'typeorm';

export async function checkOrderUnderwayTool(
	params: {
		customerEmail?: string;
		customerName?: string;
		items?: Array<{ productId?: string | number | bigint; productName?: string; quantity: number; taxAmount?: number }>;
	},
	db: any,
	conversationUuid: string
) {
	const { customerEmail, customerName, items } = params || ({} as any);
	if (!customerEmail && !customerName) {
		throw new Error('Either customerEmail or customerName is required');
	}

	// Validate database connection and get repositories
	if (!db || !db.isInitialized) {
		throw new Error('Database connection is not properly initialized');
	}

	const conversationRepo = db.getRepository('Conversation');
	const customerRepo = db.getRepository('Customer');
	const orderRepo = db.getRepository('Order');

	// Read conversation ownership to derive store/user context
	const convo = await conversationRepo.findOne({
		where: { uuid: conversationUuid, isDeleted: false },
		select: ['id', 'userId', 'storeId', 'createdBy', 'context'],
	});
	if (!convo) throw new Error('Conversation not found');
	// Find customer using email or name
	let customerIdBigInt: bigint;
	if (customerEmail) {
		// Look for existing customer by email
		const foundCustomers = await filterCustomers({ email: customerEmail, storeId: convo.storeId }, db);
		if (!foundCustomers || foundCustomers.length === 0) {
			return { underway: false, message: 'No existing customer found for the provided email.' } as const;
		}
		const exact = foundCustomers.find((c: any) => (c.email || '').toLowerCase() === customerEmail.toLowerCase());
		customerIdBigInt = (exact || foundCustomers[0]).id as unknown as bigint;
	} else {
		// Look for existing customer by name
		const foundCustomers = await filterCustomers({ name: customerName, storeId: convo.storeId }, db);
		if (!foundCustomers || foundCustomers.length === 0) {
			return { underway: false, message: 'No existing customer found for the provided name.' } as const;
		}
		customerIdBigInt = foundCustomers[0].id as unknown as bigint;
	}

	// Store customer context and recent orders
	try {
		const customerRecord = await customerRepo.findOne({
			where: { id: customerIdBigInt, storeId: convo.storeId, isDeleted: false },
		});
		const customerOrders = await orderRepo.find({
			where: { isDeleted: false, storeId: convo.storeId, customerId: customerIdBigInt },
			order: { createdAt: 'desc' },
			relations: { items: true },
			take: 5,
		});
		const currentCtx = await conversationRepo.findOne({
			where: { id: convo.id as unknown as bigint },
			select: ['context'],
		});
		const nextCtx = { ...((currentCtx as any)?.context || {}), customer: customerRecord, customerOrders };
		await conversationRepo.update(
			{ id: convo.id as unknown as bigint },
			{ context: nextCtx }
		);
	} catch {}
	const underwayOrders = await orderRepo.find({
		where: {
			isDeleted: false,
			storeId: convo.storeId,
			customerId: customerIdBigInt,
			status: In(UNDERWAY_STATUSES),
		},
		relations: { items: true },
		order: { createdAt: 'desc' },
		take: 10,
	});
	if (!underwayOrders || underwayOrders.length === 0) {
		return { underway: false, message: 'No order currently underway.' } as const;
	}
	if (items && items.length > 0) {
		let preparedIncoming: Array<{ productId: bigint; quantity: number }> = [];
		try {
			const resolved = await resolveItemsAgainstCatalog(items, convo.storeId, db);
			preparedIncoming = resolved.map((it) => ({ productId: it.productId as bigint, quantity: it.quantity }));
		} catch {}
		const incomingKey = (list: Array<{ productId: bigint; quantity: number }>) =>
			list
				.slice()
				.sort((a, b) => (a.productId > b.productId ? 1 : -1))
				.map((it) => `${it.productId.toString()}@${it.quantity}`)
				.join('|');
		const targetKey = preparedIncoming.length > 0 ? incomingKey(preparedIncoming) : undefined;
		const similar = underwayOrders.find((o: any) => {
			if (!o.items || o.items.length === 0 || !targetKey) return true;
			const k = incomingKey(
				o.items.map((it: any) => ({ productId: it.productId as bigint, quantity: Number(it.quantity) })),
			);
			return k === targetKey;
		});
		if (similar) {
			return {
				underway: true,
				orderId: similar.id,
				orderNumber: similar.orderNumber,
				status: similar.status,
				total: similar.total,
				message: `An order is already underway for ${customerEmail || customerName}: ${similar.orderNumber}. Status: ${similar.status}.`,
			};
		}
	}
	const latest = underwayOrders[0];
	return {
		underway: true,
		orderId: latest.id,
		orderNumber: latest.orderNumber,
		status: latest.status,
		total: latest.total,
		message: `An order is already underway for ${customerEmail || customerName}: ${latest.orderNumber}. Status: ${latest.status}.`,
	};
}

export const checkOrderUnderway = createTool(checkOrderUnderwayTool, {
	name: 'checkOrderUnderway',
	description:
		'Check if there is already an order underway for a customer. Provide either customerEmail or customerName. Optionally pass items to check for a similar order.',
	parameterTypes: {
		customerEmail: { type: 'string', optional: true },
		customerName: { type: 'string', optional: true },
		items: {
			type: 'array',
			optional: true,
			items: {
				type: 'object',
				properties: {
					productId: { type: 'string', optional: true },
					productName: { type: 'string', optional: true },
					quantity: { type: 'number' },
					taxAmount: { type: 'number', optional: true },
				},
				required: ['quantity'],
			},
		},
	},
	requiredParams: [],
});
