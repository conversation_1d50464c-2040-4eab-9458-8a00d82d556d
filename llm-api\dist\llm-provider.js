"use strict";
/**
 * LLM Provider configuration and management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMProvider = void 0;
exports.setLlmProvider = setLlmProvider;
exports.setLlmProviderFromEnvironmentVariables = setLlmProviderFromEnvironmentVariables;
/**
 * Static configuration for the LLM provider.
 *
 * Values set here are used by the runner unless overridden by environment variables.
 */
class LLMProvider {
    static config = {};
    /**
     * Get current API key
     */
    static get apiKey() {
        return this.config.apiKey;
    }
    /**
     * Get current base URL
     */
    static get baseUrl() {
        return this.config.baseUrl;
    }
    /**
     * Get current model
     */
    static get model() {
        return this.config.model;
    }
    /**
     * Set the configuration
     */
    static setConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * Reset configuration to defaults
     */
    static reset() {
        this.config = {};
    }
}
exports.LLMProvider = LLMProvider;
/**
 * Set static credentials and endpoint for the LLM provider used by the runner.
 *
 * @param apiKey - Provider API key
 * @param baseUrl - Optional custom base URL for the provider (e.g., self-hosted endpoint)
 */
function setLlmProvider(apiKey, baseUrl, model) {
    LLMProvider.setConfig({ apiKey, baseUrl, model });
}
/**
 * Explicitly load credentials from environment variables and set provider config.
 * This function must be called to read from process.env; the provider does not
 * auto-read environment variables by default.
 */
function setLlmProviderFromEnvironmentVariables() {
    const { API_KEY, BASE_URL, MODEL } = process.env;
    LLMProvider.setConfig({ apiKey: API_KEY, baseUrl: BASE_URL, model: MODEL });
}
//# sourceMappingURL=llm-provider.js.map