/**
 * LLM Provider configuration and management
 */

export interface LLMConfig {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
}

/**
 * Static configuration for the LLM provider.
 * 
 * Values set here are used by the runner unless overridden by environment variables.
 */
export class LLMProvider {
  private static config: LLMConfig = {};
  
  /**
   * Get current API key
   */
  static get apiKey(): string | undefined {
    return this.config.apiKey;
  }
  
  /**
   * Get current base URL
   */
  static get baseUrl(): string | undefined {
    return this.config.baseUrl;
  }
  
  /**
   * Get current model
   */
  static get model(): string | undefined {
    return this.config.model;
  }
  
  /**
   * Set the configuration
   */
  static setConfig(config: LLMConfig): void {
    this.config = { ...this.config, ...config };
  }
  
  /**
   * Reset configuration to defaults
   */
  static reset(): void {
    this.config = {};
  }
}

/**
 * Set static credentials and endpoint for the LLM provider used by the runner.
 * 
 * @param apiKey - Provider API key
 * @param baseUrl - Optional custom base URL for the provider (e.g., self-hosted endpoint)
 */
export function setLlmProvider(apiKey: string, baseUrl?: string, model?: string): void {
  LLMProvider.setConfig({ apiKey, baseUrl, model });
}

/**
 * Explicitly load credentials from environment variables and set provider config.
 * This function must be called to read from process.env; the provider does not
 * auto-read environment variables by default.
 */
export function setLlmProviderFromEnvironmentVariables(): void {
  const { API_KEY, BASE_URL, MODEL } = process.env;
  LLMProvider.setConfig({ apiKey: API_KEY, baseUrl: BASE_URL, model: MODEL });
}
