"use strict";
/**
 * Common types used throughout the Agent SDK
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmResponseResult = void 0;
exports.estimateLlmCost = estimateLlmCost;
/**
 * Result class that holds both the LLM message response and tool calls
 */
class LlmResponseResult {
    messageResponse;
    toolCalls;
    toolOutputs;
    executionDetails;
    llmCosts;
    constructor(messageResponse, toolCalls, toolOutputs, executionDetails, llmCosts) {
        this.messageResponse = messageResponse;
        this.toolCalls = toolCalls;
        this.toolOutputs = toolOutputs;
        this.executionDetails = executionDetails;
        this.llmCosts = llmCosts;
    }
    /**
     * Get the message response content
     */
    get content() {
        return this.messageResponse;
    }
    /**
     * Get all tool calls made by the LLM
     */
    get calls() {
        return this.toolCalls;
    }
    /**
     * Get all tool outputs/results
     */
    get outputs() {
        return this.toolOutputs;
    }
    /**
     * Check if any tool calls were made
     */
    get hasToolCalls() {
        return this.toolCalls.length > 0;
    }
    /**
     * Get tool call by name
     */
    getToolCallByName(name) {
        return this.toolCalls.find(tc => tc.function?.name === name);
    }
    /**
     * Get tool output by tool call ID
     */
    getToolOutputById(toolCallId) {
        return this.toolOutputs.find(to => to.tool_call_id === toolCallId);
    }
    /**
     * Get execution details for a specific tool call
     */
    getExecutionDetail(toolCallId) {
        return this.executionDetails.find(detail => detail.toolCallId === toolCallId);
    }
    /**
     * Get all successful tool executions
     */
    get successfulExecutions() {
        return this.executionDetails.filter(detail => detail.success);
    }
    /**
     * Get all failed tool executions
     */
    get failedExecutions() {
        return this.executionDetails.filter(detail => !detail.success);
    }
    /**
     * Get total execution time for all tool calls
     */
    get totalExecutionTime() {
        return this.executionDetails.reduce((total, detail) => total + detail.executionTime, 0);
    }
    /**
     * Get total cost for all LLM calls
     */
    get totalCost() {
        return this.llmCosts.totalCost;
    }
    /**
     * Get total input tokens
     */
    get totalInputTokens() {
        return this.llmCosts.totalInputTokens;
    }
    /**
     * Get total output tokens
     */
    get totalOutputTokens() {
        return this.llmCosts.totalOutputTokens;
    }
    /**
     * Get total execution time for all LLM calls
     */
    get totalLlmExecutionTime() {
        return this.llmCosts.costBreakdown.reduce((total, call) => total + call.executionTime, 0);
    }
    /**
     * Get detailed information about the initial LLM call
     */
    get initialCall() {
        return this.llmCosts.costBreakdown.find(call => call.callType === 'initial');
    }
    /**
     * Get detailed information about the followup LLM call
     */
    get followupCall() {
        return this.llmCosts.costBreakdown.find(call => call.callType === 'followup');
    }
    /**
     * Get all LLM call details with timing
     */
    get llmCallDetails() {
        return this.llmCosts.costBreakdown;
    }
}
exports.LlmResponseResult = LlmResponseResult;
/**
 * Utility function to estimate LLM costs based on model and token counts
 * Prices are approximate and may vary by provider
 */
function estimateLlmCost(model, inputTokens, outputTokens) {
    // Default to GPT-4o-mini pricing if model is not recognized
    const modelPricing = getModelPricing(model);
    const inputCost = (inputTokens / 1000) * modelPricing.inputCostPer1k;
    const outputCost = (outputTokens / 1000) * modelPricing.outputCostPer1k;
    return inputCost + outputCost;
}
/**
 * Get pricing information for a specific model
 */
function getModelPricing(model) {
    const modelLower = model.toLowerCase();
    // OpenAI models (approximate pricing as of 2024)
    if (modelLower.includes('gpt-4o')) {
        return { inputCostPer1k: 0.0025, outputCostPer1k: 0.01 };
    }
    if (modelLower.includes('gpt-4-turbo') || modelLower.includes('gpt-4-1106')) {
        return { inputCostPer1k: 0.01, outputCostPer1k: 0.03 };
    }
    if (modelLower.includes('gpt-4')) {
        return { inputCostPer1k: 0.03, outputCostPer1k: 0.06 };
    }
    if (modelLower.includes('gpt-3.5-turbo')) {
        return { inputCostPer1k: 0.0005, outputCostPer1k: 0.0015 };
    }
    // Anthropic models
    if (modelLower.includes('claude-3-opus')) {
        return { inputCostPer1k: 0.015, outputCostPer1k: 0.075 };
    }
    if (modelLower.includes('claude-3-sonnet')) {
        return { inputCostPer1k: 0.003, outputCostPer1k: 0.015 };
    }
    if (modelLower.includes('claude-3-haiku')) {
        return { inputCostPer1k: 0.00025, outputCostPer1k: 0.00125 };
    }
    // Google models
    if (modelLower.includes('gemini-2.0')) {
        return { inputCostPer1k: 0.0005, outputCostPer1k: 0.0015 };
    }
    if (modelLower.includes('gemini-1.5')) {
        return { inputCostPer1k: 0.000375, outputCostPer1k: 0.001125 };
    }
    // Default to GPT-4o-mini pricing
    return { inputCostPer1k: 0.0025, outputCostPer1k: 0.01 };
}
//# sourceMappingURL=types.js.map