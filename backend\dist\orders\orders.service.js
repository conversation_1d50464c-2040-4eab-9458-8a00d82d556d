"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = exports.OrderPriorityService = exports.OrderStatusService = void 0;
exports.computeOrderTotals = computeOrderTotals;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const order_entity_1 = require("./order.entity");
const order_item_entity_1 = require("./order-item.entity");
var OrderStatusService;
(function (OrderStatusService) {
    OrderStatusService["DRAFT"] = "draft";
    OrderStatusService["PENDING"] = "pending";
    OrderStatusService["CONFIRMED"] = "confirmed";
    OrderStatusService["PROCESSING"] = "processing";
    OrderStatusService["SHIPPED"] = "shipped";
    OrderStatusService["DELIVERED"] = "delivered";
    OrderStatusService["CANCELLED"] = "cancelled";
    OrderStatusService["RETURNED"] = "returned";
})(OrderStatusService || (exports.OrderStatusService = OrderStatusService = {}));
var OrderPriorityService;
(function (OrderPriorityService) {
    OrderPriorityService["LOW"] = "low";
    OrderPriorityService["NORMAL"] = "normal";
    OrderPriorityService["HIGH"] = "high";
    OrderPriorityService["URGENT"] = "urgent";
})(OrderPriorityService || (exports.OrderPriorityService = OrderPriorityService = {}));
function computeOrderTotals(items, useTax, taxRate) {
    const subtotal = items.reduce((sum, it) => sum + it.quantity * it.unitPrice, 0);
    const taxFromRate = useTax ? subtotal * taxRate : 0;
    const taxFromLines = items.reduce((sum, it) => sum + (it.taxAmount ?? 0), 0);
    const taxAmount = Math.max(taxFromRate, taxFromLines);
    const total = subtotal + taxAmount;
    return { subtotal, taxAmount, total };
}
async function generateNextOrderNumber(tx) {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, '0');
    const dd = String(now.getDate()).padStart(2, '0');
    const datePart = `${yyyy}${mm}${dd}`;
    const prefix = `ORD-${datePart}-`;
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
    const startOfNextDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0, 0);
    const todayCount = await tx.getRepository(order_entity_1.Order).count({
        where: {
            createdAt: (0, typeorm_2.Between)(startOfDay, startOfNextDay)
        }
    });
    return `${prefix}${String(todayCount + 157).padStart(4, '0')}`;
}
let OrdersService = class OrdersService {
    constructor(orderRepository, orderItemRepository) {
        this.orderRepository = orderRepository;
        this.orderItemRepository = orderItemRepository;
    }
    async createOrder(input) {
        return this.orderRepository.manager.transaction(async (tx) => {
            const orderNumber = await generateNextOrderNumber(tx);
            const { subtotal, taxAmount, total } = computeOrderTotals(input.items, input.useTax || false, input.taxRate || 0);
            const order = new order_entity_1.Order();
            order.orderNumber = orderNumber;
            order.status = input.status || OrderStatusService.PENDING;
            order.priority = input.priority || OrderPriorityService.NORMAL;
            order.useTax = input.useTax || false;
            order.taxRate = input.taxRate || 0;
            order.orderDate = input.orderDate || new Date();
            order.expectedDeliveryDate = input.expectedDeliveryDate;
            order.preferredDeliveryLocation = input.preferredDeliveryLocation;
            order.subtotal = subtotal;
            order.taxAmount = taxAmount;
            order.total = total;
            order.userId = input.userId;
            order.storeId = input.storeId;
            order.customerId = input.customerId;
            order.createdBy = input.createdBy;
            const savedOrder = await tx.save(order);
            const orderItems = input.items.map(item => {
                const lineTotal = item.quantity * item.unitPrice;
                const orderItem = new order_item_entity_1.OrderItem();
                orderItem.orderId = savedOrder.id;
                orderItem.productId = item.productId;
                orderItem.productName = item.productName;
                orderItem.quantity = item.quantity;
                orderItem.unitPrice = item.unitPrice;
                orderItem.lineTotal = lineTotal;
                orderItem.taxAmount = item.taxAmount || 0;
                orderItem.createdBy = input.createdBy;
                return orderItem;
            });
            await tx.save(orderItems);
            return this.findById(savedOrder.id);
        });
    }
    async updateOrder(input) {
        return this.orderRepository.manager.transaction(async (tx) => {
            const order = await tx.findOne(order_entity_1.Order, { where: { id: input.id } });
            if (!order) {
                throw new Error('Order not found');
            }
            const updateData = {
                updatedBy: input.updatedBy,
            };
            if (input.status !== undefined)
                updateData.status = input.status;
            if (input.priority !== undefined)
                updateData.priority = input.priority;
            if (input.useTax !== undefined)
                updateData.useTax = input.useTax;
            if (input.taxRate !== undefined)
                updateData.taxRate = input.taxRate;
            if (input.orderDate !== undefined)
                updateData.orderDate = input.orderDate;
            if (input.expectedDeliveryDate !== undefined)
                updateData.expectedDeliveryDate = input.expectedDeliveryDate;
            if (input.preferredDeliveryLocation !== undefined)
                updateData.preferredDeliveryLocation = input.preferredDeliveryLocation;
            if (input.cancellationReason !== undefined)
                updateData.cancellationReason = input.cancellationReason;
            if (input.items) {
                await tx.delete(order_item_entity_1.OrderItem, { orderId: input.id });
                const orderItems = input.items.map(item => {
                    const lineTotal = item.quantity * item.unitPrice;
                    const orderItem = new order_item_entity_1.OrderItem();
                    orderItem.orderId = input.id;
                    orderItem.productId = item.productId;
                    orderItem.productName = item.productName;
                    orderItem.quantity = item.quantity;
                    orderItem.unitPrice = item.unitPrice;
                    orderItem.lineTotal = lineTotal;
                    orderItem.taxAmount = item.taxAmount || 0;
                    orderItem.createdBy = input.updatedBy;
                    return orderItem;
                });
                await tx.save(orderItems);
                const { subtotal, taxAmount, total } = computeOrderTotals(input.items, order.useTax, order.taxRate);
                updateData.subtotal = subtotal;
                updateData.taxAmount = taxAmount;
                updateData.total = total;
            }
            await tx.update(order_entity_1.Order, { id: input.id }, updateData);
            return this.findById(input.id);
        });
    }
    async findById(id) {
        return this.orderRepository.findOne({
            where: { id },
            relations: ['customer', 'store', 'orderItems', 'orderItems.product'],
        });
    }
    async findByOrderNumber(orderNumber) {
        return this.orderRepository.findOne({
            where: { orderNumber },
            relations: ['customer', 'store', 'orderItems', 'orderItems.product'],
        });
    }
    async findOrders(filter, page = 1, limit = 20) {
        const where = { isDeleted: false };
        if (filter.storeId)
            where.storeId = filter.storeId;
        if (filter.userId)
            where.userId = filter.userId;
        if (filter.customerId)
            where.customerId = filter.customerId;
        if (filter.status)
            where.status = filter.status;
        if (filter.priority)
            where.priority = filter.priority;
        const [orders, total] = await this.orderRepository.findAndCount({
            where,
            relations: ['customer', 'store', 'orderItems'],
            skip: (page - 1) * limit,
            take: limit,
            order: { createdAt: 'DESC' },
        });
        return { orders, total };
    }
    async deleteOrder(id, userId) {
        await this.orderRepository.update(id, {
            isDeleted: true,
            updatedBy: userId,
        });
    }
    async getOrderStats(storeId, startDate, endDate) {
        const where = { storeId, isDeleted: false };
        if (startDate || endDate) {
            where.createdAt = {};
            if (startDate)
                where.createdAt.gte = startDate;
            if (endDate)
                where.createdAt.lte = endDate;
        }
        const orders = await this.orderRepository.find({ where });
        const totalOrders = orders.length;
        const totalRevenue = orders.reduce((sum, order) => sum + Number(order.total), 0);
        const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
        const ordersByStatus = orders.reduce((acc, order) => {
            acc[order.status] = (acc[order.status] || 0) + 1;
            return acc;
        }, {});
        return {
            totalOrders,
            totalRevenue,
            averageOrderValue,
            ordersByStatus,
        };
    }
};
exports.OrdersService = OrdersService;
exports.OrdersService = OrdersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(order_entity_1.Order)),
    __param(1, (0, typeorm_1.InjectRepository)(order_item_entity_1.OrderItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], OrdersService);
//# sourceMappingURL=orders.service.js.map