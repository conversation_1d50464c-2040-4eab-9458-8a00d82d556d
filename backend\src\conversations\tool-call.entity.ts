import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('tool_calls')
export class ToolCall {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'varchar' })
  toolName: string;

  @Column({ type: 'json' })
  parameters: any;

  @Column({ type: 'json', nullable: true })
  result: any;

  @Column({ type: 'varchar', nullable: true })
  status: string;

  @Column({ type: 'text', nullable: true })
  error: string;

  @Column({ type: 'int', default: 0 })
  duration: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  cost: number;

  @Column({ type: 'int', nullable: true })
  inputTokens: number;

  @Column({ type: 'int', nullable: true })
  outputTokens: number;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  userId: string;

  @Column({ type: 'bigint', nullable: true })
  conversationId: string;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.toolCalls)
  user: any;

  @ManyToOne('User', (user: any) => user.createdToolCalls)
  createdByUser: any;

  @ManyToOne('User', (user: any) => user.updatedToolCalls)
  updatedByUser: any;

  @ManyToOne('Conversation', (conversation: any) => conversation.toolCalls)
  conversation: any;
}
