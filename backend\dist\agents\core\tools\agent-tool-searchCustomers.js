"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchCustomers = void 0;
exports.searchCustomersTool = searchCustomersTool;
const dist_1 = require("../../../../../llm-api/dist");
const customer_service_1 = require("../../services/customer.service");
async function searchCustomersTool(params, db, conversationUuid) {
    let { name, email, phone } = params || {};
    name = typeof name === 'string' ? name.trim() : undefined;
    email = typeof email === 'string' ? email.trim() : undefined;
    phone = typeof phone === 'string' ? phone.trim() : undefined;
    if (!name)
        name = undefined;
    if (!email)
        email = undefined;
    if (!phone)
        phone = undefined;
    if (!db || !db.isInitialized) {
        throw new Error('Database connection is not properly initialized');
    }
    const conversationRepo = db.getRepository('Conversation');
    const orderRepo = db.getRepository('Order');
    const convo = await conversationRepo.findOne({
        where: { uuid: conversationUuid, isDeleted: false },
        select: { id: true, storeId: true },
    });
    if (!convo)
        throw new Error('Conversation not found');
    const customers = await (0, customer_service_1.filterCustomers)({
        storeId: convo.storeId,
        email,
        name,
        phone,
    }, db);
    try {
        const currentCtx = await conversationRepo.findOne({
            where: { id: convo.id },
            select: { context: true },
        });
        const baseCtx = { ...(currentCtx?.context || {}), customers };
        let nextCtx = baseCtx;
        if (Array.isArray(customers) && customers.length === 1) {
            const selected = customers[0];
            const customerOrders = await orderRepo.find({
                where: { isDeleted: false, storeId: convo.storeId, customerId: selected.id },
                order: { createdAt: 'asc' },
                relations: { items: true },
                take: 5,
            });
            nextCtx = { ...baseCtx, customer: { id: selected.id, name: selected.name, email: selected.email, phone: selected.phone }, customerOrders };
        }
        await conversationRepo.update({ id: convo.id }, { context: nextCtx });
    }
    catch { }
    return {
        count: Array.isArray(customers) ? customers.length : 0,
        customers: (customers || []).map((c) => ({ id: c.id, name: c.name, email: c.email, phone: c.phone })),
    };
}
exports.searchCustomers = (0, dist_1.createTool)(searchCustomersTool, {
    name: 'searchCustomers',
    description: 'Search customers by name, email, or phone within the store. Trims inputs and ignores empty fields; combines provided filters. Saves results to context.customers and, when a single match is found, also sets context.customer and their recent orders.',
    parameterTypes: {
        name: { type: 'string', optional: true },
        email: { type: 'string', optional: true },
        phone: { type: 'string', optional: true },
    },
});
//# sourceMappingURL=agent-tool-searchCustomers.js.map