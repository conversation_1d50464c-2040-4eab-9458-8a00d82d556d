"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.typeToJsonSchema = typeToJsonSchema;
exports.buildParametersSchema = buildParametersSchema;
exports.addToolMetadata = addToolMetadata;
/**
 * Convert TypeScript types to JSON schema for OpenAI function calling
 */
function typeToJsonSchema(type) {
    // Handle primitive types
    if (type === String || type === 'string') {
        return { type: 'string' };
    }
    if (type === Number || type === 'number') {
        return { type: 'number' };
    }
    if (type === Boolean || type === 'boolean') {
        return { type: 'boolean' };
    }
    if (Array.isArray(type)) {
        return {
            type: 'array',
            items: typeToJsonSchema(type[0] || String)
        };
    }
    if (type === Object || typeof type === 'object') {
        return { type: 'object' };
    }
    // Default to string for unknown types
    return { type: 'string' };
}
/**
 * Build parameter schema from function metadata
 */
function buildParametersSchema(func, parameterTypes, requiredParams) {
    const properties = {};
    const required = requiredParams || [];
    // If parameter types are provided, use them
    if (parameterTypes) {
        for (const [name, type] of Object.entries(parameterTypes)) {
            // Check if the type is already a JSON schema object
            if (typeof type === 'object' && type !== null && 'type' in type) {
                properties[name] = type;
            }
            else {
                // Convert TypeScript types to JSON schema
                properties[name] = typeToJsonSchema(type);
            }
        }
    }
    return {
        type: 'object',
        properties,
        required
    };
}
/**
 * Decorator-like function to add tool metadata to functions
 */
function addToolMetadata(func, description, parameterTypes, requiredParams, explicitName) {
    const toolDescription = description || func.name || 'Tool function';
    // Ensure a non-empty, valid function name for OpenAI tools
    const deriveToolName = () => {
        if (explicitName && explicitName.trim().length > 0)
            return explicitName.trim();
        const base = func.name;
        if (base && base.trim().length > 0)
            return base.trim();
        // Derive from description
        const fromDesc = (toolDescription || '').toLowerCase().replace(/[^a-z0-9]+/g, '_').replace(/^_+|_+$/g, '');
        if (fromDesc && fromDesc.length > 0)
            return fromDesc;
        // Fallback unique name
        return `tool_${Math.random().toString(36).slice(2, 10)}`;
    };
    const toolName = deriveToolName();
    const parametersSchema = buildParametersSchema(func, parameterTypes, requiredParams);
    func._toolSpec = {
        type: 'function',
        function: {
            name: toolName,
            description: toolDescription,
            parameters: parametersSchema
        }
    };
    return func;
}
//# sourceMappingURL=schema.js.map