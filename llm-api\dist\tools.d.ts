import { ToolFunction, ToolSpec } from './types';
/**
 * Decorator interface for tool function configuration
 */
export interface ToolConfig {
    name?: string;
    description?: string;
    parameterTypes?: Record<string, any>;
    requiredParams?: string[];
}
/**
 * Decorator function to mark a function as an LLM-callable tool.
 *
 * This function adds metadata to the function that allows it to be
 * exposed to the LLM as a tool with proper schema information.
 *
 * @param config - Configuration for the tool including description and parameter types
 * @returns Decorator function
 */
export declare function functionTool(config?: ToolConfig): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
/**
 * Function to manually mark a function as a tool (alternative to decorator)
 *
 * @param func - The function to be marked as a tool
 * @param config - Configuration for the tool
 * @returns The original function with added tool metadata
 */
export declare function createTool(func: ToolFunction, config?: ToolConfig): ToolFunction;
/**
 * Extract tool specifications from a function with tool metadata
 */
export declare function getToolSpec(func: ToolFunction): ToolSpec | null;
/**
 * Check if a function has tool metadata
 */
export declare function isToolFunction(func: ToolFunction): boolean;
//# sourceMappingURL=tools.d.ts.map