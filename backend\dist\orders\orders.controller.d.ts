import { OrdersService, CreateOrderInput, UpdateOrderInput } from './orders.service';
import { PaginatedResponse } from '../types/pagination';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    createOrder(createOrderInput: CreateOrderInput): Promise<import("./order.entity").Order>;
    getOrders(page?: string, limit?: string, storeId?: string, userId?: string, customerId?: string, status?: string, priority?: string): Promise<PaginatedResponse<any>>;
    getOrder(id: string): Promise<import("./order.entity").Order>;
    getOrderByNumber(orderNumber: string): Promise<import("./order.entity").Order>;
    updateOrder(id: string, updateOrderInput: UpdateOrderInput): Promise<import("./order.entity").Order>;
    deleteOrder(id: string, userId: string): Promise<void>;
    getOrderStats(storeId: string, startDate?: string, endDate?: string): Promise<{
        totalOrders: number;
        totalRevenue: number;
        averageOrderValue: number;
        ordersByStatus: Record<string, number>;
    }>;
}
