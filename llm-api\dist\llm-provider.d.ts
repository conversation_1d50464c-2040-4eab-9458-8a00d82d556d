/**
 * LLM Provider configuration and management
 */
export interface LLMConfig {
    apiKey?: string;
    baseUrl?: string;
    model?: string;
}
/**
 * Static configuration for the LLM provider.
 *
 * Values set here are used by the runner unless overridden by environment variables.
 */
export declare class LLMProvider {
    private static config;
    /**
     * Get current API key
     */
    static get apiKey(): string | undefined;
    /**
     * Get current base URL
     */
    static get baseUrl(): string | undefined;
    /**
     * Get current model
     */
    static get model(): string | undefined;
    /**
     * Set the configuration
     */
    static setConfig(config: LLMConfig): void;
    /**
     * Reset configuration to defaults
     */
    static reset(): void;
}
/**
 * Set static credentials and endpoint for the LLM provider used by the runner.
 *
 * @param apiKey - Provider API key
 * @param baseUrl - Optional custom base URL for the provider (e.g., self-hosted endpoint)
 */
export declare function setLlmProvider(apiKey: string, baseUrl?: string, model?: string): void;
/**
 * Explicitly load credentials from environment variables and set provider config.
 * This function must be called to read from process.env; the provider does not
 * auto-read environment variables by default.
 */
export declare function setLlmProviderFromEnvironmentVariables(): void;
//# sourceMappingURL=llm-provider.d.ts.map