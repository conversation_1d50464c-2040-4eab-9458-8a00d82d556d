"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Migration1756495890690 = void 0;
class Migration1756495890690 {
    constructor() {
        this.name = 'Migration1756495890690';
    }
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "messages" ADD "cost" numeric(10,6)`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "executionTime" integer`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "inputTokens" integer`);
        await queryRunner.query(`ALTER TABLE "messages" ADD "outputTokens" integer`);
        await queryRunner.query(`ALTER TYPE "public"."orders_status_enum" RENAME TO "orders_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" TYPE "public"."orders_status_enum" USING "status"::"text"::"public"."orders_status_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."orders_priority_enum" RENAME TO "orders_priority_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."orders_priority_enum" AS ENUM('low', 'normal', 'high', 'urgent')`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" TYPE "public"."orders_priority_enum" USING "priority"::"text"::"public"."orders_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" SET DEFAULT 'normal'`);
        await queryRunner.query(`DROP TYPE "public"."orders_priority_enum_old"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "cost"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "executionTime"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "inputTokens"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP COLUMN "outputTokens"`);
        await queryRunner.query(`ALTER TYPE "public"."orders_status_enum_old" RENAME TO "orders_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" TYPE "public"."orders_status_enum_old" USING "status"::"text"::"public"."orders_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum_old" AS ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned')`);
        await queryRunner.query(`ALTER TYPE "public"."orders_priority_enum_old" RENAME TO "orders_priority_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" SET DEFAULT 'normal'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" TYPE "public"."orders_priority_enum_old" USING "priority"::"text"::"public"."orders_priority_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "priority" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."orders_priority_enum_old" AS ENUM('low', 'normal', 'high', 'urgent')`);
    }
}
exports.Migration1756495890690 = Migration1756495890690;
//# sourceMappingURL=1756495890690-Migration.js.map