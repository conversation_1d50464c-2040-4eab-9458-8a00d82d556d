"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
const tools_1 = require("./tools");
/**
 * Agent definition with instructions and tools.
 *
 * An Agent represents an LLM-powered assistant with specific instructions
 * and a set of tools it can use to accomplish tasks.
 */
class Agent {
    name;
    instructions;
    tools;
    handoffDescription;
    constructor(name, instructions, tools = [], handoffDescription) {
        this.name = name;
        this.instructions = instructions;
        this.tools = tools;
        this.handoffDescription = handoffDescription;
    }
    /**
     * Get OpenAI-compatible tool specifications.
     *
     * @returns List of tool specifications in the format expected by OpenAI API
     */
    getToolSpecs() {
        const specs = [];
        for (const tool of this.tools) {
            const spec = (0, tools_1.getToolSpec)(tool);
            if (spec) {
                specs.push(spec);
            }
        }
        return specs;
    }
    /**
     * Get mapping from tool names to their implementations.
     *
     * @returns Map of tool names to their function implementations
     */
    getToolMap() {
        const mapping = new Map();
        for (const tool of this.tools) {
            const spec = (0, tools_1.getToolSpec)(tool);
            const exposedName = spec?.function?.name || tool.name;
            mapping.set(exposedName, tool);
        }
        return mapping;
    }
    /**
     * Add a tool to the agent
     */
    addTool(tool) {
        this.tools.push(tool);
    }
    /**
     * Remove a tool from the agent
     */
    removeTool(toolName) {
        const index = this.tools.findIndex(tool => tool.name === toolName);
        if (index !== -1) {
            this.tools.splice(index, 1);
            return true;
        }
        return false;
    }
    /**
     * Check if the agent has a specific tool
     */
    hasTool(toolName) {
        return this.tools.some(tool => tool.name === toolName);
    }
}
exports.Agent = Agent;
//# sourceMappingURL=agent.js.map