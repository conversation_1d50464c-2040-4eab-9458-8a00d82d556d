/**
 * Common types used throughout the Agent SDK
 */

import type { 
  ChatCompletionTool, 
  ChatCompletionMessageParam,
  ChatCompletionMessageToolCall 
} from 'openai/resources/chat/completions';

export interface ToolSpec extends ChatCompletionTool {}

export interface JsonSchema {
  type: string;
  properties?: Record<string, JsonSchema>;
  required?: string[];
  items?: JsonSchema;
  default?: any;
  [key: string]: any; // Index signature for OpenAI compatibility
}

export interface Message {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content?: string | null;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolOutput {
  role: 'tool';
  content: string;
  tool_call_id: string;
}

export type ToolFunction = (...args: any[]) => any;

/**
 * Result class that holds both the LLM message response and tool calls
 */
export class LlmResponseResult {
  constructor(
    public readonly messageResponse: string,
    public readonly toolCalls: ChatCompletionMessageToolCall[],
    public readonly toolOutputs: ToolOutput[],
    public readonly executionDetails: ToolExecutionDetail[],
    public readonly llmCosts: LlmCostInfo
  ) {}

  /**
   * Get the message response content
   */
  get content(): string {
    return this.messageResponse;
  }

  /**
   * Get all tool calls made by the LLM
   */
  get calls(): ChatCompletionMessageToolCall[] {
    return this.toolCalls;
  }

  /**
   * Get all tool outputs/results
   */
  get outputs(): ToolOutput[] {
    return this.toolOutputs;
  }

  /**
   * Check if any tool calls were made
   */
  get hasToolCalls(): boolean {
    return this.toolCalls.length > 0;
  }

  /**
   * Get tool call by name
   */
  getToolCallByName(name: string): ChatCompletionMessageToolCall | undefined {
    return this.toolCalls.find(tc => tc.function?.name === name);
  }

  /**
   * Get tool output by tool call ID
   */
  getToolOutputById(toolCallId: string): ToolOutput | undefined {
    return this.toolOutputs.find(to => to.tool_call_id === toolCallId);
  }

  /**
   * Get execution details for a specific tool call
   */
  getExecutionDetail(toolCallId: string): ToolExecutionDetail | undefined {
    return this.executionDetails.find(detail => detail.toolCallId === toolCallId);
  }

  /**
   * Get all successful tool executions
   */
  get successfulExecutions(): ToolExecutionDetail[] {
    return this.executionDetails.filter(detail => detail.success);
  }

  /**
   * Get all failed tool executions
   */
  get failedExecutions(): ToolExecutionDetail[] {
    return this.executionDetails.filter(detail => !detail.success);
  }

  /**
   * Get total execution time for all tool calls
   */
  get totalExecutionTime(): number {
    return this.executionDetails.reduce((total, detail) => total + detail.executionTime, 0);
  }

  /**
   * Get total cost for all LLM calls
   */
  get totalCost(): number {
    return this.llmCosts.totalCost;
  }

  /**
   * Get total input tokens
   */
  get totalInputTokens(): number {
    return this.llmCosts.totalInputTokens;
  }

  /**
   * Get total output tokens
   */
  get totalOutputTokens(): number {
    return this.llmCosts.totalOutputTokens;
  }

  /**
   * Get total execution time for all LLM calls
   */
  get totalLlmExecutionTime(): number {
    return this.llmCosts.costBreakdown.reduce((total, call) => total + call.executionTime, 0);
  }

  /**
   * Get detailed information about the initial LLM call
   */
  get initialCall(): LlmCallCost | undefined {
    return this.llmCosts.costBreakdown.find(call => call.callType === 'initial');
  }

  /**
   * Get detailed information about the followup LLM call
   */
  get followupCall(): LlmCallCost | undefined {
    return this.llmCosts.costBreakdown.find(call => call.callType === 'followup');
  }

  /**
   * Get all LLM call details with timing
   */
  get llmCallDetails(): LlmCallCost[] {
    return this.llmCosts.costBreakdown;
  }
}

/**
 * Detailed information about tool execution
 */
export interface ToolExecutionDetail {
  toolCallId: string;
  toolName: string;
  executionTime: number;
  success: boolean;
  errorMessage?: string;
  startTime: number;
  endTime: number;
}

/**
 * Cost information for LLM calls
 */
export interface LlmCostInfo {
  model: string;
  totalInputTokens: number;
  totalOutputTokens: number;
  totalCost: number;
  costBreakdown: LlmCallCost[];
}

/**
 * Cost breakdown for individual LLM calls
 */
export interface LlmCallCost {
  callType: 'initial' | 'followup';
  inputTokens: number;
  outputTokens: number;
  cost: number;
  model: string;
  startTime: number;
  endTime: number;
  executionTime: number;
}

/**
 * Utility function to estimate LLM costs based on model and token counts
 * Prices are approximate and may vary by provider
 */
export function estimateLlmCost(
  model: string, 
  inputTokens: number, 
  outputTokens: number
): number {
  // Default to GPT-4o-mini pricing if model is not recognized
  const modelPricing = getModelPricing(model);
  
  const inputCost = (inputTokens / 1000) * modelPricing.inputCostPer1k;
  const outputCost = (outputTokens / 1000) * modelPricing.outputCostPer1k;
  
  return inputCost + outputCost;
}

/**
 * Get pricing information for a specific model
 */
function getModelPricing(model: string): { inputCostPer1k: number; outputCostPer1k: number } {
  const modelLower = model.toLowerCase();
  
  // OpenAI models (approximate pricing as of 2024)
  if (modelLower.includes('gpt-4o')) {
    return { inputCostPer1k: 0.0025, outputCostPer1k: 0.01 };
  }
  if (modelLower.includes('gpt-4-turbo') || modelLower.includes('gpt-4-1106')) {
    return { inputCostPer1k: 0.01, outputCostPer1k: 0.03 };
  }
  if (modelLower.includes('gpt-4')) {
    return { inputCostPer1k: 0.03, outputCostPer1k: 0.06 };
  }
  if (modelLower.includes('gpt-3.5-turbo')) {
    return { inputCostPer1k: 0.0005, outputCostPer1k: 0.0015 };
  }
  
  // Anthropic models
  if (modelLower.includes('claude-3-opus')) {
    return { inputCostPer1k: 0.015, outputCostPer1k: 0.075 };
  }
  if (modelLower.includes('claude-3-sonnet')) {
    return { inputCostPer1k: 0.003, outputCostPer1k: 0.015 };
  }
  if (modelLower.includes('claude-3-haiku')) {
    return { inputCostPer1k: 0.00025, outputCostPer1k: 0.00125 };
  }
  
  // Google models
  if (modelLower.includes('gemini-2.0')) {
    return { inputCostPer1k: 0.0005, outputCostPer1k: 0.0015 };
  }
  if (modelLower.includes('gemini-1.5')) {
    return { inputCostPer1k: 0.000375, outputCostPer1k: 0.001125 };
  }
  
  // Default to GPT-4o-mini pricing
  return { inputCostPer1k: 0.0025, outputCostPer1k: 0.01 };
}

// Helper type to convert our Message to OpenAI format
export type OpenAIMessage = ChatCompletionMessageParam;
