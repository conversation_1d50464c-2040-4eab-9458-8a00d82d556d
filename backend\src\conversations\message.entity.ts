import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';

@Entity('messages')
export class Message {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ type: 'varchar' })
  role: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'bigint' })
  createdBy: string;

  @Column({ nullable: true, type: 'bigint' })
  updatedBy: string;

  @Column({ type: 'bigint' })
  conversationId: string;

  @Column({ type: 'bigint' })
  userId: string;

  // Performance metrics
  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  cost: number;

  @Column({ type: 'int', nullable: true })
  executionTime: number;

  @Column({ type: 'int', nullable: true })
  inputTokens: number;

  @Column({ type: 'int', nullable: true })
  outputTokens: number;

  // Relations - using string literals to avoid circular dependency
  @ManyToOne('User', (user: any) => user.messages)
  user: any;

  @ManyToOne('Conversation', (conversation: any) => conversation.messages)
  conversation: any;
}
