#!/usr/bin/env node

/**
 * Example Node.js script to run the LLM API test
 * This script shows how to set up the environment and run the test programmatically
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up environment for LLM API test...\n');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
const exampleEnvPath = path.join(__dirname, 'example.env');

if (!fs.existsSync(envPath)) {
    console.log('📝 Creating .env file from example...');
    try {
        fs.copyFileSync(exampleEnvPath, envPath);
        console.log('⚠️  Please edit .env file and add your actual API_KEY');
        console.log('   Then run this script again');
        process.exit(1);
    } catch (error) {
        console.error('❌ Failed to create .env file:', error.message);
        process.exit(1);
    }
}

// Read and validate .env file
console.log('📖 Reading environment variables...');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
            envVars[key.trim()] = valueParts.join('=').trim();
        }
    }
});

// Check if API_KEY is set
if (!envVars.API_KEY || envVars.API_KEY === 'your_openrouter_api_key_here') {
    console.log('❌ API_KEY not set in .env file');
    console.log('   Please edit .env file and add your actual OpenRouter API key');
    process.exit(1);
}

console.log('✅ Environment setup complete!');
console.log(`🔑 API Key: ${envVars.API_KEY.substring(0, 10)}...`);
console.log(`🌐 Base URL: ${envVars.BASE_URL || 'https://openrouter.ai/api/v1'}`);
console.log(`🤖 Model: ${envVars.MODEL || 'google/gemini-2.5-flash-lite'}`);
console.log('');

// Set environment variables for the current process
Object.entries(envVars).forEach(([key, value]) => {
    if (value !== 'your_openrouter_api_key_here') {
        process.env[key] = value;
    }
});

// Run the test
console.log('🧪 Running LLM API test...\n');
try {
    const result = execSync('npm run test:llm', { 
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit',
        env: process.env
    });
    console.log('\n✅ Test completed successfully!');
} catch (error) {
    console.error('\n❌ Test failed with error code:', error.status);
    process.exit(error.status || 1);
}
