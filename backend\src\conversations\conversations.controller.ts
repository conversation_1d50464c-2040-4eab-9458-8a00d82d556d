import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ConversationsService } from './conversations.service';
import { Conversation } from './conversation.entity';
import { ToolCall } from './tool-call.entity';

@ApiTags('conversations')
@Controller('conversations')
export class ConversationsController {
  constructor(private readonly conversationsService: ConversationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new conversation' })
  @ApiResponse({ status: 201, description: 'Conversation created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createConversationDto: Partial<Conversation> & { userId?: string | number; storeId?: string | number; createdBy?: string | number }) {
    return this.conversationsService.create(createConversationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all conversations' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findAll() {
    return this.conversationsService.findAll();
  }

  @Get('store/:storeId')
  @ApiOperation({ summary: 'Get conversations by store ID' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findByStoreId(@Param('storeId') storeId: string) {
    return this.conversationsService.findByStoreId(storeId);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get conversations by user ID' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findByUserId(@Param('userId') userId: string) {
    return this.conversationsService.findByUserId(userId);
  }

  @Get('uuid/:uuid')
  @ApiOperation({ summary: 'Get conversation by UUID' })
  @ApiResponse({ status: 200, description: 'Conversation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  findByUuid(@Param('uuid') uuid: string) {
    return this.conversationsService.findByUuid(uuid);
  }

  @Get('uuid/:uuid/unified-timeline')
  @ApiOperation({ summary: 'Get unified timeline by conversation UUID' })
  @ApiResponse({ status: 200, description: 'Unified timeline retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  getUnifiedTimelineByUuid(
    @Param('uuid') uuid: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '50'
  ) {
    return this.conversationsService.getUnifiedTimelineByUuid(
      uuid,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
  }

  @Post('uuid/:uuid/messages')
  @ApiOperation({ summary: 'Add a message to conversation by UUID' })
  @ApiResponse({ status: 201, description: 'Message added successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  addMessageByUuid(
    @Param('uuid') uuid: string,
    @Body() messageData: {
      content: string;
      createdBy: string;
      agentId?: string;
      userId?: string;
      customerId?: string;
      imageUrl?: string;
      videoUrl?: string;
      attachmentUrl?: string;
      attachmentType?: string;
      cost?: number;
      executionTime?: number;
      inputTokens?: number;
      outputTokens?: number;
    }
  ) {
    return this.conversationsService.addMessageByUuid(uuid, messageData);
  }

  @Get(':id/timeline')
  @ApiOperation({ summary: 'Get timeline by conversation ID' })
  @ApiResponse({ status: 200, description: 'Timeline retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  getTimeline(
    @Param('id') id: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '50'
  ) {
    return this.conversationsService.getTimeline(
      id,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
  }

  @Get(':id/unified-timeline')
  @ApiOperation({ summary: 'Get unified timeline by conversation ID' })
  @ApiResponse({ status: 200, description: 'Unified timeline retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  getUnifiedTimeline(
    @Param('id') id: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '50'
  ) {
    return this.conversationsService.getUnifiedTimeline(
      id,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
  }

  @Post(':id/messages')
  @ApiOperation({ summary: 'Add a message to conversation by ID' })
  @ApiResponse({ status: 201, description: 'Message added successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  addMessage(
    @Param('id') id: string,
    @Body() messageData: {
      content: string;
      senderType: string;
      senderId?: string | number;
    }
  ) {
    // Transform the frontend data format to match our service method
    const transformedData = {
      content: messageData.content,
      createdBy: messageData.senderId?.toString() || '1',
      userId: messageData.senderId?.toString(),
      // Set role based on senderType
      ...(messageData.senderType === 'agent' ? { agentId: 'system' } : { customerId: messageData.senderId?.toString() })
    };

    return this.conversationsService.addMessage(id, transformedData);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a conversation by ID' })
  @ApiResponse({ status: 200, description: 'Conversation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  findOne(@Param('id') id: string) {
    return this.conversationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a conversation' })
  @ApiResponse({ status: 200, description: 'Conversation updated successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  update(@Param('id') id: string, @Body() updateConversationDto: Partial<Conversation>) {
    return this.conversationsService.update(id, updateConversationDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a conversation' })
  @ApiResponse({ status: 200, description: 'Conversation deleted successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  remove(@Param('id') id: string) {
    return this.conversationsService.remove(id);
  }

  // Tool Call endpoints
  @Post('tool-calls')
  @ApiOperation({ summary: 'Create a new tool call' })
  @ApiResponse({ status: 201, description: 'Tool call created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createToolCall(@Body() createToolCallDto: Partial<ToolCall> & {
    userId?: string | number;
    cost?: number;
    inputTokens?: number;
    outputTokens?: number;
    duration?: number;
  }) {
    return this.conversationsService.createToolCall(createToolCallDto);
  }

  @Post('uuid/:uuid/tool-calls')
  @ApiOperation({ summary: 'Add a tool call to conversation by UUID' })
  @ApiResponse({ status: 201, description: 'Tool call added successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  addToolCallByUuid(
    @Param('uuid') uuid: string,
    @Body() toolCallData: {
      toolName: string;
      parameters: any;
      result?: any;
      status?: string;
      error?: string;
      duration?: number;
      cost?: number;
      inputTokens?: number;
      outputTokens?: number;
      createdBy: string;
      userId?: string;
    }
  ) {
    return this.conversationsService.addToolCallByUuid(uuid, toolCallData);
  }

  @Get('tool-calls')
  @ApiOperation({ summary: 'Get all tool calls' })
  @ApiResponse({ status: 200, description: 'Tool calls retrieved successfully' })
  findAllToolCalls() {
    return this.conversationsService.findAllToolCalls();
  }

  @Get('tool-calls/conversation/:conversationId')
  @ApiOperation({ summary: 'Get tool calls by conversation ID' })
  @ApiResponse({ status: 200, description: 'Tool calls retrieved successfully' })
  findToolCallsByConversationId(@Param('conversationId') conversationId: string) {
    return this.conversationsService.findToolCallsByConversationId(conversationId);
  }

  @Get('tool-calls/:id')
  @ApiOperation({ summary: 'Get a tool call by ID' })
  @ApiResponse({ status: 200, description: 'Tool call retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  findToolCallById(@Param('id') id: string) {
    return this.conversationsService.findToolCallById(id);
  }

  @Patch('tool-calls/:id')
  @ApiOperation({ summary: 'Update a tool call' })
  @ApiResponse({ status: 200, description: 'Tool call updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  updateToolCall(@Param('id') id: string, @Body() updateToolCallDto: Partial<ToolCall>) {
    return this.conversationsService.updateToolCall(id, updateToolCallDto);
  }

  @Delete('tool-calls/:id')
  @ApiOperation({ summary: 'Delete a tool call' })
  @ApiResponse({ status: 200, description: 'Tool call deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool call not found' })
  removeToolCall(@Param('id') id: string) {
    return this.conversationsService.removeToolCall(id);
  }
}
