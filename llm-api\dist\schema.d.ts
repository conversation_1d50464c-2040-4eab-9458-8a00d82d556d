import { JsonSchema, ToolFunction } from './types';
/**
 * Convert TypeScript types to JSON schema for OpenAI function calling
 */
export declare function typeToJsonSchema(type: any): JsonSchema;
/**
 * Build parameter schema from function metadata
 */
export declare function buildParametersSchema(func: ToolFunction, parameterTypes?: Record<string, any>, requiredParams?: string[]): JsonSchema;
/**
 * Decorator-like function to add tool metadata to functions
 */
export declare function addToolMetadata(func: ToolFunction, description?: string, parameterTypes?: Record<string, any>, requiredParams?: string[], explicitName?: string): ToolFunction;
//# sourceMappingURL=schema.d.ts.map